/**
 * Plugin to minimize and use ejs template syntax in index.html.
 * https://github.com/anncwb/vite-plugin-html
 */
import type { PluginOption } from 'vite';
import { createHtmlPlugin } from 'vite-plugin-html';
import { loadEnv } from 'vite';

export function configHtmlPlugin({ isBuild }: { isBuild: boolean }) {
  // 获取环境变量
  const env = loadEnv(process.env.NODE_ENV || 'development', process.cwd());

  const htmlPlugin: PluginOption[] = createHtmlPlugin({
    minify: isBuild,
    inject: {
      data: {
        // 默认环境变量
        VITE_GLOB_APP_TITLE: env.VITE_GLOB_APP_TITLE || '智慧料库',
        // 其他可能需要的环境变量
        ...env,
      },
    },
  });
  return htmlPlugin;
}
