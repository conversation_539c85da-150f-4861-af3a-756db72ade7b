import { defineApplicationConfig } from '@vben/vite-config';
import { entryEnvPlugin } from './control/helpers/index';
import path from 'node:path';

export default defineApplicationConfig(({ env }) => {
  return {
    overrides: {
      optimizeDeps: {
        include: [
          'echarts',
          'qrcode',
          '@iconify/iconify',
          'ant-design-vue/es/locale/zh_CN',
          'ant-design-vue/es/locale/en_US',
          'lodash-es',
        ],
      },
      server: {
        open: false, // 项目启动后，自动打开
        warmup: {
          clientFiles: ['./index.html', './src/{views,components}/*'],
        },
      },
      build: {
        rollupOptions: {
          input: {
            main: path.resolve(__dirname, 'index.html'),
            control: path.resolve(__dirname, 'control/index.html'),
            'control-media': path.resolve(__dirname, 'control-media/index.html'),
          },
        },
      },
      plugins: [
        entryEnvPlugin({
          envConfig: {
            control: {
              VITE_GLOB_APP_TITLE: env.VITE_CONTROL_GLOB_APP_TITLE,
              VITE_PROXY_ORIGIN: env.VITE_CONTROL_PROXY_ORIGIN,
              VITE_GLOB_API_URL: env.VITE_CONTROL_GLOB_API_URL || '/control-api',
            },
          },
        })
      ],
    },
  }
});
