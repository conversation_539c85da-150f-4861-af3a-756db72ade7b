import { defineApplicationConfig } from '@vben/vite-config';
import path from 'node:path';

export default defineApplicationConfig({
  overrides: {
    optimizeDeps: {
      include: [
        'echarts',
        'qrcode',
        '@iconify/iconify',
        'ant-design-vue/es/locale/zh_CN',
        'ant-design-vue/es/locale/en_US',
        'lodash-es',
      ],
    },
    server: {
      open: false, // 项目启动后，自动打开
      warmup: {
        clientFiles: ['./index.html', './src/{views,components}/*'],
      },
    },
    build: {
      rollupOptions: {
        input: {
          main: path.resolve(__dirname, 'index.html'),
          control: path.resolve(__dirname, 'control/index.html'),
          'control-media': path.resolve(__dirname, 'control-media/index.html'),
        },
      },
    },
  },
});
