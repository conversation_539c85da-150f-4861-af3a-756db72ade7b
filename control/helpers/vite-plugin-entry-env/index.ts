import type { Plugin } from 'vite';
import path from 'node:path';

/**
 * 入口环境变量配置类型
 */
export interface EntryEnvConfig {
  [entryName: string]: Record<string, string | number | boolean>;
}

/**
 * 插件选项类型
 */
export interface EntryEnvPluginOptions {
  /**
   * 按入口名称配置的环境变量
   */
  envConfig: EntryEnvConfig;
  /**
   * 是否启用调试日志
   */
  debug?: boolean;
}

/**
 * 为不同入口页面设置环境变量的 Vite 插件
 * 
 * @param options 插件配置选项
 * @returns Vite 插件
 * 
 * @example
 * ```typescript
 * // vite.config.ts
 * import { entryEnvPlugin } from './control/helpers/vite-plugin-entry-env';
 * 
 * export default defineConfig({
 *   plugins: [
 *     entryEnvPlugin({
 *       envConfig: {
 *         main: {
 *           NODE_ENV: 'production',
 *           VITE_APP_TITLE: 'Main Application',
 *           VITE_API_URL: 'https://api.main.com'
 *         },
 *         control: {
 *           NODE_ENV: 'development',
 *           VITE_APP_TITLE: 'Control Panel',
 *           VITE_API_URL: 'https://api.control.com'
 *         },
 *         'control-media': {
 *           NODE_ENV: 'production',
 *           VITE_APP_TITLE: 'Media Control',
 *           VITE_API_URL: 'https://api.media.com'
 *         }
 *       },
 *       debug: true
 *     })
 *   ]
 * });
 * ```
 */
export function entryEnvPlugin(options: EntryEnvPluginOptions): Plugin {
  const { envConfig, debug = false } = options;
  
  // 存储入口文件到入口名称的映射
  const entryFileMap = new Map<string, string>();
  // 存储模块到入口的映射关系
  const moduleEntryMap = new Map<string, string>();

  // 辅助方法：获取模块所属的入口
  function getModuleEntry(moduleId: string): string | undefined {
    const normalizedId = path.resolve(moduleId);

    // 首先检查是否是入口文件
    if (entryFileMap.has(normalizedId)) {
      return entryFileMap.get(normalizedId);
    }

    // 检查模块映射
    if (moduleEntryMap.has(normalizedId)) {
      return moduleEntryMap.get(normalizedId);
    }

    // 尝试通过文件路径推断入口
    // 按路径长度排序，优先匹配更具体的路径
    const sortedEntries = Array.from(entryFileMap.entries()).sort((a, b) => {
      const dirA = path.dirname(a[0]);
      const dirB = path.dirname(b[0]);
      return dirB.length - dirA.length; // 长路径优先
    });

    for (const [entryPath, entryName] of sortedEntries) {
      const entryDir = path.dirname(entryPath);
      if (normalizedId.startsWith(entryDir)) {
        moduleEntryMap.set(normalizedId, entryName);
        if (debug) {
          console.log(`[entry-env] 模块 ${normalizedId} 属于入口: ${entryName}`);
        }
        return entryName;
      }
    }

    return undefined;
  }

  // 辅助方法：从 HTML 转换上下文中获取入口名称
  function getEntryNameFromContext(context: any): string | undefined {
    // 从 context 中获取当前处理的文件路径
    const filename = context.filename;
    if (!filename) {
      return undefined;
    }

    const normalizedPath = path.resolve(filename);

    // 检查是否是入口文件
    if (entryFileMap.has(normalizedPath)) {
      return entryFileMap.get(normalizedPath);
    }

    // 通过文件路径推断入口
    for (const [entryPath, entryName] of entryFileMap.entries()) {
      if (normalizedPath === entryPath) {
        return entryName;
      }
    }

    return undefined;
  }

  // 辅助方法：转义正则表达式特殊字符
  function escapeRegExp(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }
  
  return {
    name: 'vite-plugin-entry-env',
    
    configResolved(config) {
      // 解析入口文件配置，建立文件路径到入口名称的映射
      const inputs = config.build?.rollupOptions?.input;
      if (inputs && typeof inputs === 'object') {
        Object.entries(inputs).forEach(([entryName, entryPath]) => {
          const normalizedPath = path.resolve(entryPath as string);
          entryFileMap.set(normalizedPath, entryName);
          
          if (debug) {
            console.log(`[entry-env] 映射入口: ${entryName} -> ${normalizedPath}`);
          }
        });
      }
    },
    
    buildStart() {
      // 清空模块映射
      moduleEntryMap.clear();
    },
    
    resolveId(id, importer) {
      // 跟踪模块的导入关系，确定模块属于哪个入口
      if (importer) {
        const importerEntry = getModuleEntry(importer);
        if (importerEntry) {
          const resolvedId = path.resolve(path.dirname(importer), id);
          moduleEntryMap.set(resolvedId, importerEntry);
          
          if (debug) {
            console.log(`[entry-env] 模块 ${resolvedId} 属于入口: ${importerEntry}`);
          }
        }
      }
      return null;
    },
    
    transform(code, id) {
      // 确定当前模块属于哪个入口
      const entryName = getModuleEntry(id);
      if (!entryName || !envConfig[entryName]) {
        return null;
      }

      const envVars = envConfig[entryName];
      let transformedCode = code;
      let hasChanges = false;

      // 替换 process.env.XXX
      Object.entries(envVars).forEach(([key, value]) => {
        const processEnvPattern = new RegExp(`process\\.env\\.${key}\\b`, 'g');
        const replacement = JSON.stringify(value);

        if (processEnvPattern.test(transformedCode)) {
          transformedCode = transformedCode.replace(processEnvPattern, replacement);
          hasChanges = true;

          if (debug) {
            console.log(`[entry-env] 替换 process.env.${key} = ${replacement} (入口: ${entryName})`);
          }
        }
      });

      // 替换 import.meta.env.XXX
      Object.entries(envVars).forEach(([key, value]) => {
        const importMetaEnvPattern = new RegExp(`import\\.meta\\.env\\.${key}\\b`, 'g');
        const replacement = JSON.stringify(value);

        if (importMetaEnvPattern.test(transformedCode)) {
          transformedCode = transformedCode.replace(importMetaEnvPattern, replacement);
          hasChanges = true;

          if (debug) {
            console.log(`[entry-env] 替换 import.meta.env.${key} = ${replacement} (入口: ${entryName})`);
          }
        }
      });

      return hasChanges ? { code: transformedCode, map: null } : null;
    },

    transformIndexHtml: {
      order: 'post',
      handler(html, context) {
        // 获取当前处理的入口名称
        const entryName = getEntryNameFromContext(context);
        if (!entryName || !envConfig[entryName]) {
          return html;
        }

        const envVars = envConfig[entryName];
        let transformedHtml = html;
        let hasChanges = false;

        // 替换 HTML 中的环境变量值
        Object.entries(envVars).forEach(([key, value]) => {
          const replacement = String(value);

          // 方法1: 替换 EJS 模板语法（如果还没被处理）
          const htmlTemplatePattern = new RegExp(`<%=\\s*${key}\\s*%>`, 'g');
          if (htmlTemplatePattern.test(transformedHtml)) {
            transformedHtml = transformedHtml.replace(htmlTemplatePattern, replacement);
            hasChanges = true;

            if (debug) {
              console.log(`[entry-env] HTML模板替换 <%= ${key} %> = ${replacement} (入口: ${entryName})`);
            }
            return;
          }

          // 方法2: 如果是 VITE_GLOB_APP_TITLE，直接查找和替换现有的标题值
          if (key === 'VITE_GLOB_APP_TITLE') {
            // 查找 <title> 标签中的内容
            const titlePattern = /<title>([^<]*)<\/title>/i;
            const titleMatch = transformedHtml.match(titlePattern);
            if (titleMatch && titleMatch[1] !== replacement) {
              transformedHtml = transformedHtml.replace(titlePattern, `<title>${replacement}</title>`);
              hasChanges = true;

              if (debug) {
                console.log(`[entry-env] HTML标题替换 ${titleMatch[1]} -> ${replacement} (入口: ${entryName})`);
              }
            }

            // 查找加载页面中的标题
            const loadingTitlePattern = /<div class="app-loading-title">([^<]*)<\/div>/i;
            const loadingMatch = transformedHtml.match(loadingTitlePattern);
            if (loadingMatch && loadingMatch[1] !== replacement) {
              transformedHtml = transformedHtml.replace(loadingTitlePattern, `<div class="app-loading-title">${replacement}</div>`);
              hasChanges = true;

              if (debug) {
                console.log(`[entry-env] HTML加载标题替换 ${loadingMatch[1]} -> ${replacement} (入口: ${entryName})`);
              }
            }
          }
        });

        return hasChanges ? transformedHtml : html;
      }
    },
    

  };
}

/**
 * 默认导出插件函数
 */
export default entryEnvPlugin;
