import type { Plugin } from 'vite';
import path from 'node:path';

/**
 * 入口环境变量配置类型
 */
export interface EntryEnvConfig {
  [entryName: string]: Record<string, string | number | boolean>;
}

/**
 * 插件选项类型
 */
export interface EntryEnvPluginOptions {
  /**
   * 按入口名称配置的环境变量
   */
  envConfig: EntryEnvConfig;
  /**
   * 是否启用调试日志
   */
  debug?: boolean;
}

/**
 * 为不同入口页面设置环境变量的 Vite 插件
 * 
 * @param options 插件配置选项
 * @returns Vite 插件
 * 
 * @example
 * ```typescript
 * // vite.config.ts
 * import { entryEnvPlugin } from './control/helpers/vite-plugin-entry-env';
 * 
 * export default defineConfig({
 *   plugins: [
 *     entryEnvPlugin({
 *       envConfig: {
 *         main: {
 *           NODE_ENV: 'production',
 *           VITE_APP_TITLE: 'Main Application',
 *           VITE_API_URL: 'https://api.main.com'
 *         },
 *         control: {
 *           NODE_ENV: 'development',
 *           VITE_APP_TITLE: 'Control Panel',
 *           VITE_API_URL: 'https://api.control.com'
 *         },
 *         'control-media': {
 *           NODE_ENV: 'production',
 *           VITE_APP_TITLE: 'Media Control',
 *           VITE_API_URL: 'https://api.media.com'
 *         }
 *       },
 *       debug: true
 *     })
 *   ]
 * });
 * ```
 */
export function entryEnvPlugin(options: EntryEnvPluginOptions): Plugin {
  const { envConfig, debug = false } = options;
  
  // 存储入口文件到入口名称的映射
  const entryFileMap = new Map<string, string>();
  // 存储模块到入口的映射关系
  const moduleEntryMap = new Map<string, string>();

  // 辅助方法：获取模块所属的入口
  function getModuleEntry(moduleId: string): string | undefined {
    const normalizedId = path.resolve(moduleId);
    
    // 首先检查是否是入口文件
    if (entryFileMap.has(normalizedId)) {
      return entryFileMap.get(normalizedId);
    }
    
    // 检查模块映射
    if (moduleEntryMap.has(normalizedId)) {
      return moduleEntryMap.get(normalizedId);
    }
    
    // 尝试通过文件路径推断入口
    for (const [entryPath, entryName] of entryFileMap.entries()) {
      const entryDir = path.dirname(entryPath);
      if (normalizedId.startsWith(entryDir)) {
        moduleEntryMap.set(normalizedId, entryName);
        return entryName;
      }
    }
    
    return undefined;
  }
  
  return {
    name: 'vite-plugin-entry-env',
    
    configResolved(config) {
      // 解析入口文件配置，建立文件路径到入口名称的映射
      const inputs = config.build?.rollupOptions?.input;
      if (inputs && typeof inputs === 'object') {
        Object.entries(inputs).forEach(([entryName, entryPath]) => {
          const normalizedPath = path.resolve(entryPath as string);
          entryFileMap.set(normalizedPath, entryName);
          
          if (debug) {
            console.log(`[entry-env] 映射入口: ${entryName} -> ${normalizedPath}`);
          }
        });
      }
    },
    
    buildStart() {
      // 清空模块映射
      moduleEntryMap.clear();
    },
    
    resolveId(id, importer) {
      // 跟踪模块的导入关系，确定模块属于哪个入口
      if (importer) {
        const importerEntry = getModuleEntry(importer);
        if (importerEntry) {
          const resolvedId = path.resolve(path.dirname(importer), id);
          moduleEntryMap.set(resolvedId, importerEntry);
          
          if (debug) {
            console.log(`[entry-env] 模块 ${resolvedId} 属于入口: ${importerEntry}`);
          }
        }
      }
      return null;
    },
    
    transform(code, id) {
      // 确定当前模块属于哪个入口
      const entryName = getModuleEntry(id);
      if (!entryName || !envConfig[entryName]) {
        return null;
      }
      
      const envVars = envConfig[entryName];
      let transformedCode = code;
      let hasChanges = false;
      
      // 替换 process.env.XXX
      Object.entries(envVars).forEach(([key, value]) => {
        const processEnvPattern = new RegExp(`process\\.env\\.${key}\\b`, 'g');
        const replacement = JSON.stringify(value);
        
        if (processEnvPattern.test(transformedCode)) {
          transformedCode = transformedCode.replace(processEnvPattern, replacement);
          hasChanges = true;
          
          if (debug) {
            console.log(`[entry-env] 替换 process.env.${key} = ${replacement} (入口: ${entryName})`);
          }
        }
      });
      
      // 替换 import.meta.env.XXX
      Object.entries(envVars).forEach(([key, value]) => {
        const importMetaEnvPattern = new RegExp(`import\\.meta\\.env\\.${key}\\b`, 'g');
        const replacement = JSON.stringify(value);
        
        if (importMetaEnvPattern.test(transformedCode)) {
          transformedCode = transformedCode.replace(importMetaEnvPattern, replacement);
          hasChanges = true;
          
          if (debug) {
            console.log(`[entry-env] 替换 import.meta.env.${key} = ${replacement} (入口: ${entryName})`);
          }
        }
      });
      
      return hasChanges ? { code: transformedCode, map: null } : null;
    },
    

  };
}

/**
 * 默认导出插件函数
 */
export default entryEnvPlugin;
