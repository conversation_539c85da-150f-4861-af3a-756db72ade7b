# Vite 插件工具集

## entryEnvPlugin - 入口环境变量插件

这个插件允许您为不同的入口页面设置不同的环境变量，在构建过程中自动替换代码中的环境变量引用。

### 功能特性

- ✅ 支持为不同入口页面设置独立的环境变量
- ✅ 自动替换 `process.env.XXX` 引用
- ✅ 自动替换 `import.meta.env.XXX` 引用
- ✅ 支持字符串、数字、布尔值类型
- ✅ 提供调试日志功能
- ✅ TypeScript 类型支持

### 安装和使用

#### 1. 在 vite.config.ts 中配置插件

```typescript
import { defineApplicationConfig } from '@vben/vite-config';
import { entryEnvPlugin } from './control/helpers';
import path from 'node:path';

export default defineApplicationConfig({
  overrides: {
    plugins: [
      entryEnvPlugin({
        envConfig: {
          // 主应用环境变量
          main: {
            NODE_ENV: 'production',
            VITE_APP_TITLE: '主应用',
            VITE_API_URL: 'https://api.main.com',
            VITE_DEBUG: false
          },
          // 控制面板环境变量
          control: {
            NODE_ENV: 'development',
            VITE_APP_TITLE: '控制面板',
            VITE_API_URL: 'https://api.control.com',
            VITE_DEBUG: true
          },
          // 媒体控制环境变量
          'control-media': {
            NODE_ENV: 'production',
            VITE_APP_TITLE: '媒体控制',
            VITE_API_URL: 'https://api.media.com',
            VITE_DEBUG: false
          }
        },
        debug: true // 开启调试日志
      })
    ],
    build: {
      rollupOptions: {
        input: {
          main: path.resolve(__dirname, 'index.html'),
          control: path.resolve(__dirname, 'control/index.html'),
          'control-media': path.resolve(__dirname, 'control-media/index.html'),
        },
      },
    },
  },
});
```

#### 2. 在代码中使用环境变量

```typescript
// 在任何 TypeScript/JavaScript 文件中
console.log('应用标题:', import.meta.env.VITE_APP_TITLE);
console.log('API 地址:', import.meta.env.VITE_API_URL);
console.log('调试模式:', import.meta.env.VITE_DEBUG);

// 也支持 process.env（虽然在浏览器环境中不常用）
console.log('环境:', process.env.NODE_ENV);
```

#### 3. 构建结果

构建时，不同入口的代码会被替换为对应的值：

**main 入口的代码：**
```typescript
console.log('应用标题:', "主应用");
console.log('API 地址:', "https://api.main.com");
console.log('调试模式:', false);
```

**control 入口的代码：**
```typescript
console.log('应用标题:', "控制面板");
console.log('API 地址:', "https://api.control.com");
console.log('调试模式:', true);
```

### 配置选项

#### EntryEnvPluginOptions

| 属性 | 类型 | 必填 | 说明 |
|------|------|------|------|
| `envConfig` | `EntryEnvConfig` | ✅ | 按入口名称配置的环境变量 |
| `debug` | `boolean` | ❌ | 是否启用调试日志，默认 `false` |

#### EntryEnvConfig

```typescript
type EntryEnvConfig = {
  [entryName: string]: Record<string, string | number | boolean>;
}
```

### 工作原理

1. **入口识别**：插件会读取 `build.rollupOptions.input` 配置，建立文件路径到入口名称的映射
2. **模块跟踪**：通过 `resolveId` 钩子跟踪模块的导入关系，确定每个模块属于哪个入口
3. **代码转换**：在 `transform` 钩子中，根据模块所属入口替换对应的环境变量引用

### 注意事项

1. **入口名称匹配**：确保 `envConfig` 中的键名与 `build.rollupOptions.input` 中的入口名称完全一致
2. **变量命名**：建议使用 `VITE_` 前缀的环境变量，这是 Vite 的约定
3. **类型安全**：支持的值类型包括 `string`、`number`、`boolean`
4. **构建时替换**：环境变量在构建时被静态替换，不是运行时动态获取

### 调试

启用 `debug: true` 选项可以在控制台看到详细的替换日志：

```
[entry-env] 映射入口: main -> /path/to/index.html
[entry-env] 映射入口: control -> /path/to/control/index.html
[entry-env] 模块 /path/to/src/main.ts 属于入口: main
[entry-env] 替换 import.meta.env.VITE_APP_TITLE = "主应用" (入口: main)
```
