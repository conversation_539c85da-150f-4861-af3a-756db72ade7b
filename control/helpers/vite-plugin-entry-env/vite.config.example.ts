import { defineApplicationConfig } from '@vben/vite-config';
import { entryEnvPlugin } from './index';
import path from 'node:path';

/**
 * 使用入口环境变量插件的配置示例
 * 
 * 将此配置合并到您的 vite.config.ts 中
 */
export default defineApplicationConfig({
  overrides: {
    plugins: [
      // 添加入口环境变量插件
      entryEnvPlugin({
        envConfig: {
          // 主应用环境变量配置
          main: {
            NODE_ENV: 'production',
            VITE_APP_TITLE: '眼镜管理系统',
            VITE_APP_VERSION: '1.0.0',
            VITE_API_BASE_URL: 'https://api.glasses.com',
            VITE_ENABLE_MOCK: false,
            VITE_ENABLE_ANALYTICS: true,
          },
          
          // 控制面板环境变量配置
          control: {
            NODE_ENV: 'development',
            VITE_APP_TITLE: '控制面板',
            VITE_APP_VERSION: '1.0.0-dev',
            VITE_API_BASE_URL: 'https://api-dev.glasses.com',
            VITE_ENABLE_MOCK: true,
            VITE_ENABLE_ANALYTICS: false,
            VITE_DEBUG_MODE: true,
          },
          
          // 媒体控制环境变量配置
          'control-media': {
            NODE_ENV: 'production',
            VITE_APP_TITLE: '媒体控制中心',
            VITE_APP_VERSION: '1.0.0',
            VITE_API_BASE_URL: 'https://media-api.glasses.com',
            VITE_ENABLE_MOCK: false,
            VITE_ENABLE_ANALYTICS: true,
            VITE_MEDIA_UPLOAD_LIMIT: 100, // MB
            VITE_STREAM_QUALITY: 'HD',
          }
        },
        
        // 开启调试日志（开发时建议开启）
        debug: process.env.NODE_ENV === 'development'
      })
    ],
    
    optimizeDeps: {
      include: [
        'echarts',
        'qrcode',
        '@iconify/iconify',
        'ant-design-vue/es/locale/zh_CN',
        'ant-design-vue/es/locale/en_US',
        'lodash-es',
      ],
    },
    
    server: {
      open: false,
      warmup: {
        clientFiles: ['./index.html', './src/{views,components}/*'],
      },
    },
    
    build: {
      rollupOptions: {
        input: {
          main: path.resolve(__dirname, 'index.html'),
          control: path.resolve(__dirname, 'control/index.html'),
          'control-media': path.resolve(__dirname, 'control-media/index.html'),
        },
      },
    },
  },
});

/**
 * 在您的应用代码中使用环境变量的示例：
 * 
 * ```typescript
 * // src/config/app.ts
 * export const appConfig = {
 *   title: import.meta.env.VITE_APP_TITLE,
 *   version: import.meta.env.VITE_APP_VERSION,
 *   apiBaseUrl: import.meta.env.VITE_API_BASE_URL,
 *   enableMock: import.meta.env.VITE_ENABLE_MOCK,
 *   enableAnalytics: import.meta.env.VITE_ENABLE_ANALYTICS,
 * };
 * 
 * // 不同入口会得到不同的配置值
 * console.log('当前应用配置:', appConfig);
 * ```
 */
